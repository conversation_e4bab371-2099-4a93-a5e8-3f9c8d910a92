part of widgets;

/// 同步状态监控组件
class SyncStatusWidget extends StatelessWidget {
  final bool showManualSync;
  final VoidCallback? onManualSync;

  const SyncStatusWidget({
    Key? key,
    this.showManualSync = true,
    this.onManualSync,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SyncService>(
      init: SyncService.to,
      builder: (syncService) {
        final status = syncService?.syncStatus ?? 'idle';
        final isSyncing = syncService?.isSyncing ?? false;

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: _getStatusColor(status).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getStatusColor(status).withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              // 状态图标
              _buildStatusIcon(status, isSyncing),
              const SizedBox(width: 8),

              // 状态文本
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    OxText(
                      _getStatusText(status),
                      fontSize: 12.0,
                      fontWeight: FontWeight.w600,
                      color: _getStatusColor(status),
                    ),
                    const SizedBox(height: 2),
                    const OxText(
                      '数据会自动同步',
                      fontSize: 10.0,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ),

              // 手动同步按钮
              if (showManualSync && !isSyncing) ...[
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: onManualSync ?? () => _triggerManualSync(context),
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.sync,
                      size: 16,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],

              // 同步进度
              if (isSyncing) ...[
                const SizedBox(width: 8),
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  /// 构建状态图标
  Widget _buildStatusIcon(String status, bool isSyncing) {
    if (isSyncing) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
        ),
      );
    }

    IconData iconData;
    Color iconColor;

    switch (status) {
      case 'success':
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case 'error':
        iconData = Icons.error;
        iconColor = Colors.red;
        break;
      case 'syncing':
        iconData = Icons.sync;
        iconColor = Colors.blue;
        break;
      default:
        iconData = Icons.sync_disabled;
        iconColor = Colors.grey;
        break;
    }

    return Icon(iconData, size: 16, color: iconColor);
  }

  /// 获取状态颜色
  Color _getStatusColor(String status) {
    switch (status) {
      case 'success':
        return Colors.green;
      case 'error':
        return Colors.red;
      case 'syncing':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// 获取状态文本
  String _getStatusText(String status) {
    switch (status) {
      case 'success':
        return '同步成功';
      case 'error':
        return '同步失败';
      case 'syncing':
        return '正在同步...';
      default:
        return '未同步';
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }

  /// 触发手动同步
  void _triggerManualSync(BuildContext context) async {
    try {
      // 显示加载提示
      ShowToast.loading('正在同步数据...');

      // 执行同步
      final success = await SyncService.to.syncAll();

      ShowToast.dismiss();

      if (success) {
        ShowToast.success('同步成功');
      } else {
        ShowToast.fail('同步失败，请稍后重试');
      }
    } catch (e) {
      ShowToast.dismiss();
      ShowToast.fail('同步出错: ${e.toString()}');
    }
  }
}

/// 简化的同步状态指示器
class SimpleSyncIndicator extends StatelessWidget {
  const SimpleSyncIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SyncService>(
      init: SyncService.to,
      builder: (syncService) {
        if (syncService.isSyncing.value) {
          return const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          );
        }

        Color statusColor;
        IconData statusIcon;

        switch (syncService.syncStatus.value) {
          case 'success':
            statusColor = Colors.green;
            statusIcon = Icons.check_circle;
            break;
          case 'error':
            statusColor = Colors.red;
            statusIcon = Icons.error;
            break;
          default:
            statusColor = Colors.grey;
            statusIcon = Icons.sync_disabled;
            break;
        }

        return Icon(statusIcon, size: 16, color: statusColor);
      },
    );
  }
}

/// 同步状态页面
class SyncStatusPage extends StatelessWidget {
  const SyncStatusPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const OxText('数据同步状态'),
      ),
      body: GetBuilder<SyncService>(
        init: SyncService.to,
        builder: (syncService) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 总体同步状态
                SyncStatusWidget(
                  onManualSync: () => _performFullSync(context),
                ),

                const SizedBox(height: 24),

                // 详细同步信息
                const OxText(
                  '同步详情',
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 16),

                // 书籍同步状态
                _buildSyncItem(
                  '书籍数据',
                  Icons.book,
                  () => _syncBooks(context),
                ),

                const SizedBox(height: 12),

                // 卡片同步状态
                _buildSyncItem(
                  '卡片数据',
                  Icons.credit_card,
                  () => _syncCards(context),
                ),

                const SizedBox(height: 12),

                // 学习记录同步状态
                _buildSyncItem(
                  '学习记录',
                  Icons.history,
                  () => _syncStudyRecords(context),
                ),

                const Spacer(),

                // 同步说明
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      OxText(
                        '同步说明',
                        fontSize: 12.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                      SizedBox(height: 4),
                      OxText(
                        '• 数据会自动在后台同步\n• 手动同步可确保数据最新\n• 同步需要网络连接',
                        fontSize: 11.0,
                        color: Colors.blue,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSyncItem(String title, IconData icon, VoidCallback onSync) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: OxText(
              title,
              fontSize: 14.0,
            ),
          ),
          GestureDetector(
            onTap: onSync,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const OxText(
                '同步',
                fontSize: 12.0,
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _performFullSync(BuildContext context) async {
    ShowToast.loading('正在执行完整同步...');
    try {
      final success = await SyncService.to.syncAll();
      ShowToast.dismiss();
      if (success) {
        ShowToast.success('完整同步成功');
      } else {
        ShowToast.fail('同步失败');
      }
    } catch (e) {
      ShowToast.dismiss();
      ShowToast.fail('同步出错: ${e.toString()}');
    }
  }

  void _syncBooks(BuildContext context) async {
    ShowToast.loading('正在同步书籍数据...');
    try {
      final success = await BookDataService.to.manualSync();
      ShowToast.dismiss();
      if (success) {
        ShowToast.success('书籍同步成功');
      } else {
        ShowToast.fail('书籍同步失败');
      }
    } catch (e) {
      ShowToast.dismiss();
      ShowToast.fail('书籍同步出错: ${e.toString()}');
    }
  }

  void _syncCards(BuildContext context) async {
    ShowToast.loading('正在同步卡片数据...');
    try {
      final success = await CardDataService.to.manualSync();
      ShowToast.dismiss();
      if (success) {
        ShowToast.success('卡片同步成功');
      } else {
        ShowToast.fail('卡片同步失败');
      }
    } catch (e) {
      ShowToast.dismiss();
      ShowToast.fail('卡片同步出错: ${e.toString()}');
    }
  }

  void _syncStudyRecords(BuildContext context) async {
    ShowToast.loading('正在同步学习记录...');
    try {
      // 这里可以添加学习记录的同步逻辑
      await Future.delayed(const Duration(seconds: 1)); // 模拟同步
      ShowToast.dismiss();
      ShowToast.success('学习记录同步成功');
    } catch (e) {
      ShowToast.dismiss();
      ShowToast.fail('学习记录同步出错: ${e.toString()}');
    }
  }
}
