part of card_editor;

enum EditType { create, edit }

enum PageType { front, back, base }

enum FileType { image, audio, video, file }

class CardEditorController extends GetxController {
  static CardEditorController get to => Get.find();
  EditType editType = EditType.create;
  CardType cardType = CardType.general;
  CardModelCreate cardModelCreate = CardModelCreate();
  PageController pageController = PageController();
  // frontTextController
  TextEditingController frontTextController = TextEditingController();
  TextEditingController backTextController = TextEditingController();
  // briefTextController;
  TextEditingController baseInfoController = TextEditingController();
  CardEditorController();
  CardAsset primaryImage =
      CardAsset(type: CardAssetType.primaryImage.value, isCorrect: true);
  CardAsset secondaryImage =
      CardAsset(type: CardAssetType.secondaryImage.value, isCorrect: true);
  CardAsset primaryAudio =
      CardAsset(type: CardAssetType.primaryAudio.value, isCorrect: true);
  CardAsset secondaryAudio =
      CardAsset(type: CardAssetType.secondaryAudio.value, isCorrect: true);

  final formKey = GlobalKey<FormState>();
  // List<BookModel> bookList = [];
  List<CustomSelectItem> bookItems = [];
  // List<CustomSelectItem> selectedBookItems = [];
  List<int> lastSelectedIds = [];
  int typeVersion = 0;
  int? bookId;
  CardModel cardModel = CardModel();

  /// pageViewList
  List<Widget> pageViewList = [];
  List<Map<String, dynamic>> cardTypes = [
    {"value": CardType.general, "label": LocaleKeys.cardEditorGeneral.tr},
    {
      "value": CardType.generalMarkdown,
      "label": LocaleKeys.cardEditorGeneralMarkdown.tr
    },
    {
      "value": CardType.languageGeneral,
      "label": LocaleKeys.cardEditorLanguageGeneral.tr
    },
    {
      "value": CardType.hanziWriter,
      "label": LocaleKeys.cardEditorHanziWriter.tr
    },
  ];

  List<Map<String, dynamic>> tobeUploadFiles = [];

  /// 搜索框输入值变化回调
  late Function(String?) onInputValueChanged;
  int pageIndex = 0;

  bool questionAiVoice = false;
  bool answerAiVoice = false;
  init() {
    // await fetchBookList();
    /// 将配置里正在编辑的卡片本更新到view上
    if (AuthController.to.usr.config?.editingBookId != null) {
      bookItems = bookItems.map((e) {
        if (e.value == AuthController.to.usr.config?.editingBookId) {
          e.selected = true;
          return e;
        } else {
          return e;
        }
      }).toList();
    }

    var data = Get.arguments;
    if (data is CardModel) {
      cardModel = data;
      Console.log(cardModel);
      editType = EditType.edit;
      cardModelCreate = CardModelCreate.fromJson(data.toJson());
      baseInfoController.text = cardModelCreate.title ?? "";
      frontTextController.text = cardModelCreate.question ?? "";
      backTextController.text = cardModelCreate.answer ?? "";
      var tempPrimaryImage = cardModelCreate.cardAssets.firstWhereNullable(
          (e) => e.type == CardAssetType.primaryImage.value);
      if (tempPrimaryImage != null) {
        primaryImage = tempPrimaryImage;
      }

      var tempSecondaryImage = cardModelCreate.cardAssets.firstWhereNullable(
          (e) => e.type == CardAssetType.secondaryImage.value);
      if (tempSecondaryImage != null) {
        secondaryImage = tempSecondaryImage;
      }

      var tempPrimaryAudio = cardModelCreate.cardAssets.firstWhereNullable(
          (e) => e.type == CardAssetType.primaryAudio.value);
      if (tempPrimaryAudio != null) {
        primaryAudio = tempPrimaryAudio;
        Console.log(primaryAudio);
      }

      var tempSecondaryAudio = cardModelCreate.cardAssets.firstWhereNullable(
          (e) => e.type == CardAssetType.secondaryAudio.value);
      if (tempSecondaryAudio != null) {
        secondaryAudio = tempSecondaryAudio;
      }
      // var tempCardType = cardModelCreate.type?.cardType;
      cardType = cardModelCreate.type?.cardType ?? CardType.general;
      update();
      // Console.log(cardType);
      // if (tempCardType != null) {
      //   cardType = tempCardType;
      // } else {
      //   cardType = CardType.general;
      // }
    } else if (data is int) {
      getLastCardType();
      bookId = data;
    }

    update();
  }

  void onTap() {}

  // @override
  // void onInit() {
  //   super.onInit();
  // }

  // @override
  // void onReady() {
  //   super.onReady();
  //   // init();
  // }

  // @override
  // void onClose() {
  //   super.onClose();
  // }

  /// 当前页面改变时, 更新当前页面索引
  void onPageChanged(int value) {
    pageIndex = value;
    update();
  }

  toNextPage(int i) {
    int lastIndex = pageViewList.length - 1;
    pageIndex = pageIndex + i;
    if (pageIndex < 0) {
      pageIndex = 0;
    } else if (pageIndex >= lastIndex) {
      pageIndex = lastIndex;
    }
    pageController.jumpToPage(pageIndex);
    update();
  }

  onSave() async {
    ShowToast.loading();
    Console.log("onSave");
    await handleCardCreateData();
    await createOrUpdateCard();
    ShowToast.dismiss();
  }

  Future createAsset(Map<String, dynamic> data) async {
    var response = await OxHttp.to.dio
        .post(HttpUrl.assets, data: mdio.FormData.fromMap(data));
    if (response.statusCode == 200) {
      Console.log(response.data["data"]);
      return AssetModel.fromJson(response.data["data"]);
    }
  }

  Future createOrUpdateCard() async {
    Console.log(cardModelCreate.toJson());
    if (editType == EditType.create) {
      await asyncRequest(
        () => OxHttp.to.post(
          HttpUrl.cards,
          data: cardModelCreate.toJson(),
        ),
        onSuccess: (data) {
          // dataModel = CardModel.fromJson(data);
          Get.back();
        },
        onFailure: (error) {
          final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
          ShowToast.fail(friendlyMessage);
        },
      );
    } else {
      await asyncRequest(
        () => OxHttp.to.put(
          "${HttpUrl.cards}/${cardModel.id}",
          data: cardModelCreate.toJson(),
        ),
        onSuccess: (data) {
          // dataModel = CardModel.fromJson(data);
          update();
          Get.back();
        },
        onFailure: (error) {
          final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
          ShowToast.fail(friendlyMessage);
        },
      );
    }
  }

  // Future fetchBookList({String? key}) async {
  //   Map<String, dynamic> filters = {
  //     "user_id": AuthController.to.usr.user?.id,
  //   };
  //   if (key != null && key.isNotEmpty) {
  //     filters["name__icontains"] = key;
  //   }
  //   await asyncRequest(
  //     () => HttpUtils.to.get(
  //       HttpUrl.books,
  //       queryParameters: {"filters": jsonEncode(filters)},
  //     ),
  //     onSuccess: (data) {
  //       List<BookModel> dataModelList =
  //           data.map<BookModel>((x) => BookModel.fromJson(x)).toList();
  //       bookList = dataModelList;

  //       bookItems = bookList
  //           .map<CustomSelectItem>(
  //               (e) => CustomSelectItem(value: e.id, label: e.name ?? ""))
  //           .toList();

  //       /// 根据本地缓存更新选中状态
  //       for (var element in bookItems) {
  //         if (lastSelectedIds.contains(element.value)) {
  //           element.selected = true;
  //         }
  //       }
  //       // Console.log(lastSelectedIds.length);
  //       update();
  //     },
  //     onFailure: (error) {
  //       ShowToast.fail(error?.msg ?? "未知错误");
  //     },
  //   );
  // }

  onBookSelected(value, index) {
    bookItems[index].selected = value ?? false;
    // selectedBookItems = bookItems.where((e) => e.selected == true).toList();
    update();
  }

  toUploadImage({
    CardAssetType cardAssetType = CardAssetType.primaryImage,
  }) async {
    Get.back();
    var result = await Get.toNamed(AppRoutes.uploadImage);
    Console.log(result);
    Console.log(AppRoutes.uploadImage);
    if (result is String) {
      switch (cardAssetType) {
        case CardAssetType.primaryImage:
          primaryImage.url = result;
          break;
        case CardAssetType.secondaryImage:
          secondaryImage.url = result;
          Console.log(secondaryImage);
          break;
        default:
      }
    }
    update();
  }

  toUploadAudio(
      {CardAssetType cardAssetType = CardAssetType.primaryAudio}) async {
    Get.back(); // 先关闭弹窗
    var result = await Get.toNamed(AppRoutes.uploadAudio);
    if (result is String) {
      switch (cardAssetType) {
        case CardAssetType.primaryAudio:
          Console.log(result);
          primaryAudio.url = result;
          questionAiVoice = false;
          break;
        case CardAssetType.secondaryAudio:
          secondaryAudio.url = result;
          answerAiVoice = false;
          break;
        default:
      }
    }
    update();
  }

  void clearAsset(CardAssetType cardAssetType) {
    switch (cardAssetType) {
      case CardAssetType.primaryImage:
        primaryImage =
            CardAsset(type: CardAssetType.primaryImage.value, isCorrect: true);
        break;
      case CardAssetType.secondaryImage:
        secondaryImage = CardAsset(
            type: CardAssetType.secondaryImage.value, isCorrect: true);
        break;
      case CardAssetType.primaryAudio:
        OxAudioController.to.stop();
        primaryAudio =
            CardAsset(type: CardAssetType.primaryAudio.value, isCorrect: true);
        break;
      case CardAssetType.secondaryAudio:
        OxAudioController.to.stop();
        secondaryAudio = CardAsset(
            type: CardAssetType.secondaryAudio.value, isCorrect: true);

        break;
      default:
    }
    update();
  }

  play(CardAssetType cardAssetType) async {
    await OxAudioController.to.stop();
    switch (cardAssetType) {
      case CardAssetType.primaryAudio:
        OxAudioController.to.play(primaryAudio.url);
        break;
      case CardAssetType.secondaryAudio:
        OxAudioController.to.play(secondaryAudio.url);
        break;
      default:
    }
  }

  Future handleCardCreateData() async {
    cardModelCreate.bookId ??= bookId;
    cardModelCreate.title = baseInfoController.text;
    cardModelCreate.question = frontTextController.text;
    cardModelCreate.type = cardType.value;
    cardModelCreate.answer = backTextController.text;
    cardModelCreate.typeVersion = typeVersion;
    cardModelCreate.questionAiVoice = questionAiVoice;
    cardModelCreate.answerAiVoice = answerAiVoice;
    cardModelCreate.extra = {};
    await handleAssets();
  }

  Future handleAssets() async {
    cardModelCreate.cardAssets = [];
    if (primaryImage.url != null) {
      if (!primaryImage.url!.startsWith("http")) {
        AssetModel? asset = await createAsset({
          "file": mdio.MultipartFile.fromFileSync(primaryImage.url!),
          "type": FileType.image.name,
          "name": frontTextController.text,
          "ai_voice": false,
        });
        if (asset == null) return;
        primaryImage.assetId = asset.id;
        primaryImage.url = asset.url;
        primaryImage.name = asset.name;
      }
      cardModelCreate.cardAssets.add(primaryImage);
    }
    if (secondaryImage.url != null) {
      if (!secondaryImage.url!.startsWith("http")) {
        AssetModel? asset = await createAsset({
          "file": mdio.MultipartFile.fromFileSync(secondaryImage.url!),
          "type": FileType.image.name,
          "name": backTextController.text,
          "ai_voice": false,
        });
        if (asset == null) return;
        Console.log(asset.toJson());
        secondaryImage.assetId = asset.id;
        secondaryImage.url = asset.url;
        secondaryImage.name = asset.name;
      }
      cardModelCreate.cardAssets.add(secondaryImage);
    }

    if (questionAiVoice) {
      AssetModel? asset = await createAsset({
        "file": null,
        "type": FileType.audio.name,
        "name": frontTextController.text,
        "ai_voice": questionAiVoice,
      });
      if (asset == null) return;
      primaryAudio.assetId = asset.id;
      primaryAudio.url = asset.url;
      primaryAudio.text = asset.name;
      cardModelCreate.cardAssets.add(primaryAudio);
    } else if (primaryAudio.url != null) {
      Console.log(22222);
      if (!primaryAudio.url!.startsWith("http")) {
        AssetModel? asset = await createAsset({
          "file": mdio.MultipartFile.fromFileSync(primaryAudio.url!),
          "type": FileType.audio.name,
          "name": primaryAudio.name ?? backTextController.text,
          "ai_voice": false,
        });
        if (asset == null) return;
        primaryAudio.assetId = asset.id;
        primaryAudio.url = asset.url;
        primaryAudio.text = asset.name;
      }
      cardModelCreate.cardAssets.add(primaryAudio);
    }
    if (answerAiVoice) {
      AssetModel? asset = await createAsset({
        "file": null,
        "type": FileType.audio.name,
        "name": backTextController.text,
        "ai_voice": answerAiVoice,
      });
      if (asset == null) return;
      secondaryAudio.assetId = asset.id;
      secondaryAudio.url = asset.url;
      secondaryAudio.text = asset.name;
      cardModelCreate.cardAssets.add(secondaryAudio);
    } else if (secondaryAudio.url != null) {
      if (!secondaryAudio.url!.startsWith("http")) {
        AssetModel? asset = await createAsset({
          "file": mdio.MultipartFile.fromFileSync(secondaryAudio.url!),
          "type": FileType.audio.name,
          "name": secondaryAudio.name ?? backTextController.text,
          "ai_voice": answerAiVoice,
        });
        if (asset == null) return;
        Console.log(asset.toJson());
        secondaryAudio.assetId = asset.id;
        secondaryAudio.text = asset.name;
        secondaryAudio.url = asset.url;
      }
      cardModelCreate.cardAssets.add(secondaryAudio);
    }
  }

  setLastCardType() async {
    await StorageService.to.setString("lastCardType", cardType.value);
  }

  getLastCardType() {
    String type = StorageService.to.getString("lastCardType");
    if (type.isEmpty) return;
    cardType = CardType.values.firstWhere((e) => e.value == type);
    update();
  }
}
