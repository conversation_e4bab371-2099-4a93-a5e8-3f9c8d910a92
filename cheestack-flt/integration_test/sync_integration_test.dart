import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/main.dart' as app;
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('同步功能集成测试', () {
    setUpAll(() async {
      // 初始化应用
      await app.main();
      await Future.delayed(const Duration(seconds: 2));
    });

    testWidgets('测试同步按钮功能', (WidgetTester tester) async {
      // 等待应用完全加载
      await tester.pumpAndSettle();

      // 查找同步按钮（可能在不同的页面）
      final syncButtonFinder = find.byIcon(Icons.sync);
      
      if (syncButtonFinder.evaluate().isNotEmpty) {
        // 点击同步按钮
        await tester.tap(syncButtonFinder.first);
        await tester.pumpAndSettle();

        // 验证没有错误弹窗
        expect(find.text('同步出错'), findsNothing);
        
        // 等待同步完成
        await Future.delayed(const Duration(seconds: 3));
        await tester.pumpAndSettle();
      }
    });

    testWidgets('测试书籍创建和同步', (WidgetTester tester) async {
      await tester.pumpAndSettle();

      // 查找创建书籍按钮
      final createBookButton = find.byIcon(Icons.add);
      
      if (createBookButton.evaluate().isNotEmpty) {
        await tester.tap(createBookButton.first);
        await tester.pumpAndSettle();

        // 填写书籍信息
        final nameField = find.byType(TextField).first;
        if (nameField.evaluate().isNotEmpty) {
          await tester.enterText(nameField, '集成测试书籍');
          await tester.pumpAndSettle();

          // 查找保存按钮
          final saveButton = find.text('保存');
          if (saveButton.evaluate().isNotEmpty) {
            await tester.tap(saveButton);
            await tester.pumpAndSettle();

            // 验证书籍创建成功
            expect(find.text('集成测试书籍'), findsOneWidget);
          }
        }
      }
    });

    testWidgets('测试同步状态显示', (WidgetTester tester) async {
      await tester.pumpAndSettle();

      // 查找同步状态组件
      final syncStatusFinder = find.text('数据同步正常');
      
      if (syncStatusFinder.evaluate().isNotEmpty) {
        expect(syncStatusFinder, findsAtLeastNWidget(1));
      }
    });

    testWidgets('测试错误处理', (WidgetTester tester) async {
      await tester.pumpAndSettle();

      // 模拟网络错误情况
      // 这里可以通过修改网络配置或使用mock来测试错误处理
      
      // 验证错误处理机制
      // 应该显示适当的错误信息而不是崩溃
    });
  });

  group('数据一致性测试', () {
    testWidgets('测试本地和远程数据一致性', (WidgetTester tester) async {
      await tester.pumpAndSettle();

      // 获取同步服务
      final apiSyncService = Get.find<ApiSyncService>();
      
      // 执行同步
      final syncResult = await apiSyncService.syncFullData();
      
      // 验证同步结果
      expect(syncResult, isTrue);
      
      // 验证数据一致性
      // 这里可以添加更多的数据验证逻辑
    });

    testWidgets('测试离线模式', (WidgetTester tester) async {
      await tester.pumpAndSettle();

      // 模拟离线状态
      // 验证应用在离线状态下的行为
      
      // 创建本地数据
      final bookDataService = Get.find<BookDataService>();
      final book = await bookDataService.createBook(
        name: '离线测试书籍',
        brief: '这是一个离线创建的书籍',
        cover: '',
        privacy: 'free',
      );
      
      expect(book, isNotNull);
      expect(book?.name, equals('离线测试书籍'));
    });
  });

  group('性能测试', () {
    testWidgets('测试大量数据同步性能', (WidgetTester tester) async {
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // 执行同步
      final apiSyncService = Get.find<ApiSyncService>();
      await apiSyncService.syncFullData();
      
      stopwatch.stop();
      
      // 验证同步时间在合理范围内（例如30秒内）
      expect(stopwatch.elapsedMilliseconds, lessThan(30000));
      
      Console.log('同步耗时: ${stopwatch.elapsedMilliseconds}ms');
    });

    testWidgets('测试内存使用', (WidgetTester tester) async {
      await tester.pumpAndSettle();

      // 记录初始内存使用
      // 这里可以使用Flutter的性能监控工具
      
      // 执行多次同步操作
      final apiSyncService = Get.find<ApiSyncService>();
      for (int i = 0; i < 5; i++) {
        await apiSyncService.syncTable('books');
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      // 验证内存没有明显泄漏
      // 这里可以添加内存监控逻辑
    });
  });
}
