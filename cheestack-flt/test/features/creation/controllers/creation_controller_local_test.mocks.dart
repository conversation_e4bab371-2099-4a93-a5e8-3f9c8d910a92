// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in cheestack_flt/test/features/creation/controllers/creation_controller_local_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:cheestack_flt/models/index.dart' as _i5;
import 'package:cheestack_flt/services/index.dart' as _i3;
import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInternalFinalCallback_0<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBookDataService_1 extends _i1.SmartFake
    implements _i3.BookDataService {
  _FakeBookDataService_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [BookDataService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBookDataService extends _i1.Mock implements _i3.BookDataService {
  @override
  _i2.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i4.Future<_i3.BookDataService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue:
            _i4.Future<_i3.BookDataService>.value(_FakeBookDataService_1(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i4.Future<_i3.BookDataService>.value(_FakeBookDataService_1(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i4.Future<_i3.BookDataService>);

  @override
  _i4.Future<List<_i5.BookModel>> getUserBooks({
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserBooks,
          [],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i4.Future<List<_i5.BookModel>>.value(<_i5.BookModel>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.BookModel>>.value(<_i5.BookModel>[]),
      ) as _i4.Future<List<_i5.BookModel>>);

  @override
  _i4.Future<_i5.BookModel?> getBookById(int? bookId) => (super.noSuchMethod(
        Invocation.method(
          #getBookById,
          [bookId],
        ),
        returnValue: _i4.Future<_i5.BookModel?>.value(),
        returnValueForMissingStub: _i4.Future<_i5.BookModel?>.value(),
      ) as _i4.Future<_i5.BookModel?>);

  @override
  _i4.Future<List<_i5.BookModel>> searchBooks(
    String? keyword, {
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchBooks,
          [keyword],
          {
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i4.Future<List<_i5.BookModel>>.value(<_i5.BookModel>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.BookModel>>.value(<_i5.BookModel>[]),
      ) as _i4.Future<List<_i5.BookModel>>);

  @override
  _i4.Future<_i5.BookModel?> createBook({
    required String? name,
    String? brief,
    String? cover,
    String? privacy,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createBook,
          [],
          {
            #name: name,
            #brief: brief,
            #cover: cover,
            #privacy: privacy,
          },
        ),
        returnValue: _i4.Future<_i5.BookModel?>.value(),
        returnValueForMissingStub: _i4.Future<_i5.BookModel?>.value(),
      ) as _i4.Future<_i5.BookModel?>);

  @override
  _i4.Future<bool> updateBook(
    int? bookId, {
    String? name,
    String? brief,
    String? cover,
    String? privacy,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBook,
          [bookId],
          {
            #name: name,
            #brief: brief,
            #cover: cover,
            #privacy: privacy,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> deleteBook(int? bookId) => (super.noSuchMethod(
        Invocation.method(
          #deleteBook,
          [bookId],
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>> getBookStats(int? bookId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBookStats,
          [bookId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> getUserBookStats() => (super.noSuchMethod(
        Invocation.method(
          #getUserBookStats,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<bool> syncBooksFromApi() => (super.noSuchMethod(
        Invocation.method(
          #syncBooksFromApi,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> syncLocalBooksToApi() => (super.noSuchMethod(
        Invocation.method(
          #syncLocalBooksToApi,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
