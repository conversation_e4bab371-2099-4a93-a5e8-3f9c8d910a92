import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:cheestack_flt/shared/utils/error_handler.dart';

void main() {
  group('ErrorHandler 测试', () {
    test('应该将网络连接错误转换为用户友好的消息', () {
      // 测试各种网络连接错误
      final testCases = [
        'connection refused',
        'Connection failed',
        'network error',
        'No internet connection',
        'Network unreachable',
      ];

      for (final errorMsg in testCases) {
        final result = ErrorHandler.getUserFriendlyMessage(errorMsg);
        expect(result, '网络连接失败，请检查网络设置后重试');
      }
    });

    test('应该将超时错误转换为用户友好的消息', () {
      final testCases = [
        'timeout',
        'time out',
        'Request timeout',
        '请求超时',
      ];

      for (final errorMsg in testCases) {
        final result = ErrorHandler.getUserFriendlyMessage(errorMsg);
        expect(result, '请求超时，请检查网络后重试');
      }
    });

    test('应该将服务器错误转换为用户友好的消息', () {
      final testCases = [
        'server error',
        'internal server error',
        '500',
        '502',
        '503',
        '服务器错误',
      ];

      for (final errorMsg in testCases) {
        final result = ErrorHandler.getUserFriendlyMessage(errorMsg);
        expect(result, '服务暂时不可用，请稍后重试');
      }
    });

    test('应该将认证错误转换为用户友好的消息', () {
      final testCases = [
        'unauthorized',
        '401',
        'token expired',
        '认证失败',
        '没有权限',
      ];

      for (final errorMsg in testCases) {
        final result = ErrorHandler.getUserFriendlyMessage(errorMsg);
        expect(result, '登录已过期，请重新登录');
      }
    });

    test('应该将权限错误转换为用户友好的消息', () {
      final testCases = [
        'forbidden',
        '403',
        '权限不足',
      ];

      for (final errorMsg in testCases) {
        final result = ErrorHandler.getUserFriendlyMessage(errorMsg);
        expect(result, '权限不足，无法执行此操作');
      }
    });

    test('应该将404错误转换为用户友好的消息', () {
      final testCases = [
        'not found',
        '404',
        '不存在',
      ];

      for (final errorMsg in testCases) {
        final result = ErrorHandler.getUserFriendlyMessage(errorMsg);
        expect(result, '请求的内容不存在');
      }
    });

    test('应该将参数错误转换为用户友好的消息', () {
      final testCases = [
        'bad request',
        '400',
        '参数错误',
        'validation error',
        '数据校验错误',
      ];

      for (final errorMsg in testCases) {
        final result = ErrorHandler.getUserFriendlyMessage(errorMsg);
        expect(result, '请求参数有误，请检查后重试');
      }
    });

    test('应该处理DioException', () {
      // 测试连接超时
      final timeoutError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.connectionTimeout,
        message: 'Connection timeout',
      );

      final timeoutResult = ErrorHandler.getUserFriendlyMessage(timeoutError);
      expect(timeoutResult, '请求超时，请检查网络后重试');

      // 测试取消请求
      final cancelError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.cancel,
        message: 'Request cancelled',
      );

      final cancelResult = ErrorHandler.getUserFriendlyMessage(cancelError);
      expect(cancelResult, '操作已取消');

      // 测试网络错误
      final networkError = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.unknown,
        message: 'Network error',
      );

      final networkResult = ErrorHandler.getUserFriendlyMessage(networkError);
      expect(networkResult, '网络连接异常，请检查网络后重试');
    });

    test('应该处理带有响应数据的DioException', () {
      final errorWithResponse = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.badResponse,
        response: Response(
          requestOptions: RequestOptions(path: '/test'),
          statusCode: 400,
          data: {'msg': '用户名已存在'},
        ),
      );

      final result = ErrorHandler.getUserFriendlyMessage(errorWithResponse);
      expect(result, '用户名已存在');
    });

    test('应该保留中文错误消息', () {
      final chineseMessages = [
        '用户名已存在',
        '密码不能为空',
        '验证码错误',
        '操作成功',
      ];

      for (final msg in chineseMessages) {
        final result = ErrorHandler.getUserFriendlyMessage(msg);
        expect(result, msg);
      }
    });

    test('应该为未知错误返回通用消息', () {
      final unknownErrors = [
        'Some random error',
        'Unexpected exception',
        'Unknown error occurred',
      ];

      for (final error in unknownErrors) {
        final result = ErrorHandler.getUserFriendlyMessage(error);
        expect(result, '操作失败，请稍后重试');
      }
    });

    test('应该处理null和空值', () {
      expect(ErrorHandler.getUserFriendlyMessage(null), '操作失败，请稍后重试');
      expect(ErrorHandler.getUserFriendlyMessage(''), '操作失败，请稍后重试');
    });

    // 注意：_containsChinese是私有方法，在实际测试中我们测试公共行为
    test('应该正确处理中文错误消息', () {
      // 测试中文消息会被保留
      expect(ErrorHandler.getUserFriendlyMessage('用户名已存在'), '用户名已存在');
      expect(ErrorHandler.getUserFriendlyMessage('密码不能为空'), '密码不能为空');

      // 测试英文消息会被转换
      expect(ErrorHandler.getUserFriendlyMessage('Some random error'),
          '操作失败，请稍后重试');
    });
  });
}
